import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/material_provider.dart';
import '../../../models/material_model.dart' as model;
import '../../../models/project/project_provider.dart';
import '../../../utils/number_formatter.dart';
import '../../../widgets/gradient_button.dart';

/// Bước 4: Chọn vật liệu
class Step4Materials extends StatefulWidget {
  /// Callback khi nhấn nút tiếp tục
  final Function(List<String> selectedMaterialIds) onContinue;

  /// Callback khi nhấn nút quay lại
  final VoidCallback onBack;

  const Step4Materials({
    super.key,
    required this.onContinue,
    required this.onBack,
  });

  @override
  State<Step4Materials> createState() => _Step4MaterialsState();
}

class _Step4MaterialsState extends State<Step4Materials> {
  List<String> _selectedMaterialIds = [];
  String _searchQuery = '';
  model.MaterialType? _filterType;

  @override
  void initState() {
    super.initState();

    // Lấy dữ liệu từ dự án nháp nếu có
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final draftProject =
          Provider.of<ProjectProvider>(context, listen: false).draftProject;
      if (draftProject != null && draftProject.selectedMaterialIds.isNotEmpty) {
        setState(() {
          _selectedMaterialIds = List.from(draftProject.selectedMaterialIds);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Thanh tìm kiếm
          TextField(
            decoration: const InputDecoration(
              hintText: 'Tìm kiếm vật liệu',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),

          const SizedBox(height: 16),

          // Bộ lọc
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(null, 'Tất cả'),
                _buildFilterChip(model.MaterialType.brick, 'Gạch'),
                _buildFilterChip(model.MaterialType.sand, 'Cát'),
                _buildFilterChip(model.MaterialType.cement, 'Xi măng'),
                _buildFilterChip(model.MaterialType.steel, 'Thép'),
                _buildFilterChip(model.MaterialType.stone, 'Đá'),
                _buildFilterChip(model.MaterialType.tile, 'Gạch ốp lát'),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Danh sách vật liệu
          Expanded(
            child: Consumer<MaterialProvider>(
              builder: (context, materialProvider, child) {
                final materials =
                    materialProvider.materials.where((material) {
                      // Lọc theo tên
                      final matchesQuery =
                          _searchQuery.isEmpty ||
                          material.name.toLowerCase().contains(
                            _searchQuery.toLowerCase(),
                          );

                      // Lọc theo loại
                      final matchesType =
                          _filterType == null || material.type == _filterType;

                      return matchesQuery && matchesType;
                    }).toList();

                if (materials.isEmpty) {
                  return const Center(
                    child: Text('Không tìm thấy vật liệu phù hợp'),
                  );
                }

                return GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.8,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                  ),
                  itemCount: materials.length,
                  itemBuilder: (context, index) {
                    final material = materials[index];
                    return _buildMaterialCard(material);
                  },
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // Nút điều hướng
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: widget.onBack,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    minimumSize: const Size.fromHeight(48),
                  ),
                  child: const Text('Quay lại'),
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: GradientButton(
                  text: 'Tiếp tục',
                  onPressed:
                      _selectedMaterialIds.isNotEmpty
                          ? () {
                            // Cập nhật dự án nháp trong provider
                            Provider.of<ProjectProvider>(
                              context,
                              listen: false,
                            ).updateDraftMaterials(_selectedMaterialIds);
                            widget.onContinue(_selectedMaterialIds);
                          }
                          : null,
                  useGradient: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Xây dựng chip lọc
  Widget _buildFilterChip(model.MaterialType? type, String label) {
    final isSelected = _filterType == type;

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _filterType = selected ? type : null;
          });
        },
      ),
    );
  }

  /// Xây dựng card vật liệu
  Widget _buildMaterialCard(model.Material material) {
    final isSelected = _selectedMaterialIds.contains(material.name);

    return Card(
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color:
              isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            if (isSelected) {
              _selectedMaterialIds.remove(material.name);
            } else {
              _selectedMaterialIds.add(material.name);
            }
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon vật liệu
              Container(
                width: double.infinity,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getMaterialIcon(material.type),
                  size: 40,
                  color: Colors.grey[600],
                ),
              ),

              const SizedBox(height: 12),

              // Tên vật liệu
              Text(
                material.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              // Đơn vị
              Text(
                'Đơn vị: ${material.unit}',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),

              const SizedBox(height: 4),

              // Giá
              Text(
                'Giá: ${NumberFormatter.formatCurrency(material.pricePerUnit)} VNĐ',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const Spacer(),

              // Checkbox
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).primaryColor,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Lấy icon cho loại vật liệu
  IconData _getMaterialIcon(model.MaterialType type) {
    switch (type) {
      case model.MaterialType.brick:
        return Icons.grid_on;
      case model.MaterialType.sand:
        return Icons.grain;
      case model.MaterialType.cement:
        return Icons.inventory_2;
      case model.MaterialType.steel:
        return Icons.straighten;
      case model.MaterialType.stone:
        return Icons.landscape;
      case model.MaterialType.tile:
        return Icons.grid_4x4;
      case model.MaterialType.roofTile:
        return Icons.roofing;
      case model.MaterialType.metalSheet:
        return Icons.view_agenda;
      case model.MaterialType.insulatedMetal:
        return Icons.layers;
      case model.MaterialType.gypsum:
        return Icons.wallpaper;
      case model.MaterialType.exteriorPaint:
        return Icons.format_paint;
      case model.MaterialType.interiorPaint:
        return Icons.format_color_fill;
      case model.MaterialType.aluminumDoor:
        return Icons.door_front_door;
      case model.MaterialType.compositeDoor:
        return Icons.door_sliding;
      case model.MaterialType.labor:
        return Icons.engineering;
      case model.MaterialType.plumbingLabor:
        return Icons.plumbing;
      case model.MaterialType.plumbingMaterial:
        return Icons.water_drop;
      case model.MaterialType.concreteSand:
        return Icons.blur_circular; // Icon cho cát bê tông
      case model.MaterialType.custom:
        return Icons.category;
    }
  }
}
