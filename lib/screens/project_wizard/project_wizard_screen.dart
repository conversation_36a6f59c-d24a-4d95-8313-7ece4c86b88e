import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/project/project_provider.dart';
import '../../themes/app_colors.dart';
import '../../themes/ios16_glass/ios16_glass_theme.dart';
import 'steps/step1_basic_info.dart';
import 'steps/step2_floors_roof.dart';
import 'steps/step3_foundation_structure.dart';
import 'steps/step4_materials.dart';
import 'steps/step5_detailed_parameters.dart';

/// Màn hình wizard tạo dự án mới
class ProjectWizardScreen extends StatefulWidget {
  const ProjectWizardScreen({super.key});

  @override
  State<ProjectWizardScreen> createState() => _ProjectWizardScreenState();
}

class _ProjectWizardScreenState extends State<ProjectWizardScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;

  @override
  void initState() {
    super.initState();

    // Tạo dự án nháp mới
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ProjectProvider>(context, listen: false).clearDraftProject();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tạo công trình mới'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            _showExitConfirmDialog(context);
          },
        ),
      ),
      body: Column(
        children: [
          // Stepper indicator
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [for (int i = 0; i < 5; i++) _buildStepIndicator(i)],
            ),
          ),

          // Step title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Text(
                  _getStepTitle(_currentStep),
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const Spacer(),
                Text(
                  '${_currentStep + 1}/5',
                  style: TextStyle(
                    color: AppColors.getTextSecondary(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Step content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                // Step 1: Thông tin cơ bản
                Step1BasicInfo(
                  onContinue: (name, location, imagePath) {
                    _nextStep();
                  },
                ),

                // Step 2: Thông tin tầng và mái
                Step2FloorsRoof(
                  onContinue: () {
                    _nextStep();
                  },
                  onBack: _previousStep,
                ),

                // Step 3: Thông tin móng và kết cấu
                Step3FoundationStructure(
                  onContinue: () {
                    _nextStep();
                  },
                  onBack: _previousStep,
                ),

                // Step 4: Chọn vật liệu
                Step4Materials(
                  onContinue: (selectedMaterialIds) {
                    _nextStep();
                  },
                  onBack: _previousStep,
                ),

                // Step 5: Nhập thông số chi tiết
                Step5DetailedParameters(
                  onComplete: _createProject,
                  onBack: _previousStep,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Xây dựng chỉ báo bước
  Widget _buildStepIndicator(int step) {
    final isActive = step <= _currentStep;
    final isCompleted = step < _currentStep;

    return Expanded(
      child: Container(
        height: 4,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: isActive ? AppColors.primaryColor : Colors.grey[300],
          borderRadius: BorderRadius.circular(2),
        ),
        child:
            isCompleted
                ? const Center(
                  child: Icon(Icons.check, size: 16, color: Colors.white),
                )
                : null,
      ),
    );
  }

  /// Lấy tiêu đề của bước
  String _getStepTitle(int step) {
    switch (step) {
      case 0:
        return 'Thông tin cơ bản';
      case 1:
        return 'Thông tin tầng và mái';
      case 2:
        return 'Thông tin móng và kết cấu';
      case 3:
        return 'Chọn vật liệu';
      case 4:
        return 'Thông số chi tiết';
      default:
        return '';
    }
  }

  /// Chuyển đến bước tiếp theo
  void _nextStep() {
    if (_currentStep < 4) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Quay lại bước trước
  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Tạo dự án mới
  void _createProject() async {
    // Lưu dự án nháp
    try {
      await Provider.of<ProjectProvider>(
        context,
        listen: false,
      ).saveDraftProject();

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Đã tạo công trình mới')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Lỗi khi tạo công trình: $e')));
      }
    }
  }

  /// Hiển thị dialog xác nhận thoát với hiệu ứng kính mờ và viền gradient
  void _showExitConfirmDialog(BuildContext context) async {
    final navigator = Navigator.of(context);
    final projectProvider = Provider.of<ProjectProvider>(
      context,
      listen: false,
    );

    final result = await iOS16GlassDialog.showConfirmDialog(
      context: context,
      title: 'Xác nhận thoát',
      content: 'Bạn có chắc chắn muốn thoát? Dữ liệu đã nhập sẽ bị mất.',
      confirmText: 'Thoát',
      cancelText: 'Hủy',
      confirmColor: const Color(0xFFFF453A),
    );

    if (result == true && mounted) {
      // Xóa dự án nháp
      projectProvider.clearDraftProject();
      navigator.pop(); // Đóng màn hình wizard
    }
  }
}
